import { Router } from "express";
import { aadhaar<PERSON>ogin, abhaSearchLogin, createAbhaCard, enrollByAadhaar, enrollByAbhaddress, enrollbyMobile, enrollMobileOTP, getQRCode, getSuggestion, getUserAbhaAddressCard, getUserAbhaAddressProfile, getUserAbhaCard, getUserProfile, handleAbhaNumberAddressOtpRequest, handleVerifyAbhaNumberAddressOtp, indexLogin, profileOnShare, searchAddress, verifyMobileOtp } from "../controllers/abha.milestone.one.controller.js";
import { validateAadhaarLogin, validateAbhaNumberAddressOtp, validateabhaNumberLogin, validateAbhaRequestOtp, validateAbhaVerifyOtp, validateEnrollByAadhaar, validateEnrollByAbhaAddress, validateEnrollByMobile, validateEnrollByMobileOtp, validateIndexMobileOtp, validateSearchLogin, validateSuggestion, validateUserProfile, validateVerifyAbhaNumberAddressOtp, validateVerifyMobileOtp } from "../validation/abha/abha.validation.js";
import { FHIR_FROM_CLINIC } from "../utils/abha.api.js";
import { fhirRequestController } from "../controllers/fhir.controller.js";
import { fhirValidator } from "../validation/fhir/fhir.validation.js";

const abhaRouter = Router();

// address search
/**
 * @swagger
 * /api/abha/searchaddress:
 *   post:
 *     summary: Search for ABHA address details for login.
 *     description: Allows the client to search for login details associated with a given ABHA address.
 *     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               abhaAddress:
 *                 type: string
 *                 description: The ABHA address to search for login details.
 *                 example: "example@abdm"
 *     responses:
 *       200:
 *         description: Successfully retrieved login details for the ABHA address.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   description: Contains login details and additional metadata.
 *                   properties:
 *                     loginId:
 *                       type: string
 *                       description: The login ID associated with the ABHA address.
 *                       example: "exampleLoginId"
 *                     valid:
 *                       type: boolean
 *                       description: Indicates if the login ID is valid.
 *                       example: true
 *                     details:
 *                       type: object
 *                       description: Additional details about the login data.
 *                       example: {}
 *       400:
 *         description: Bad request due to missing or invalid input.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "ABHA address is required"
 *       500:
 *         description: Internal server error during the process.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Error fetching ABHA login data"
 */
abhaRouter.route("/searchabha").post(validateSearchLogin, async (req, res, next) => {
    try {
        return await abhaSearchLogin(req, res);
    }
    catch (e) {
        next(e)
    }
});


abhaRouter.route("/searchaddress").post( async (req, res, next) => {
    try {
        return await searchAddress(req, res);
    }
    catch (e) {
        next(e)
    }
});



abhaRouter.route("/indexlogin").post(validateIndexMobileOtp, async (req, res, next) => {
    try {
        return await indexLogin(req, res);
    }
    catch (e) {
        next(e)
    }
});



/**
 * @swagger
 * /api/abha/verifymobileotp:
 *   post:
 *     summary: Verify OTP for Mobile Login
 *     description: Verifies the OTP for mobile login using the ABHA system.
*     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               otp:
 *                 type: string
 *                 example: "123456"
 *                 description: The OTP received by the user.
 *               txnId:
 *                 type: string
 *                 example: "b6a7f3e7-0dbf-4c7d-b889-21e1d8e57a1f"
 *                 description: The transaction ID associated with the OTP request.
 *     responses:
 *       200:
 *         description: OTP verification was successful.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "OTP verified successfully"
 *       400:
 *         description: Bad request. The OTP or transaction ID may be missing or invalid.
 */
abhaRouter.route("/verifymobileotp").post(validateVerifyMobileOtp, async (req, res, next) => {
    try {
        return await verifyMobileOtp(req, res);
    }
    catch (e) {
        next(e)
    }
});




/**
 * @swagger
 * /api/abha/userprofile:
 *   get:
 *     summary: Get User Profile
 *     description: Retrieves the user's profile information from the ABHA system.
 *     tags:
 *       - ABHA
 *     parameters:
 *       - in: header
 *         name: X-token
 *         schema:
 *           type: string
 *         required: true
 *         description: Access token for authorization.
 *     responses:
 *       200:
 *         description: Successfully retrieved user profile information.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 profile:
 *                   type: object
 *                   description: User profile details.
 *                   example: 
 *                     userId: "user123"
 *                     name: "John Doe"
 *                     email: "<EMAIL>"
 *       400:
 *         description: Bad request. An error occurred while retrieving the profile.
 */

abhaRouter.route("/getuserprofile").get(validateUserProfile, async (req, res, next) => {
    try {
        return await getUserProfile(req, res);
    }
    catch (e) {
        next(e)
    }
});
abhaRouter.route("/getuserabhaaddressprofile").get(validateUserProfile, async (req, res, next) => {
    try {
        return await getUserAbhaAddressProfile(req, res);
    }
    catch (e) {
        next(e)
    }
});


// Aadhaar

/**
 * @swagger
 * /api/abha/aadhaarlogin:
 *   post:
 *     summary: Request OTP for Aadhaar Login
 *     description: Initiates an OTP request for Aadhaar-based login.
 *     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               aadhaarNumber:
 *                 type: string
 *                 example: "123456789012"
 *                 description: The user's Aadhaar number.
 *     responses:
 *       200:
 *         description: OTP request successful.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "OTP sent successfully."
 *       400:
 *         description: Bad request. Unable to initiate OTP request.
 */
abhaRouter.route("/aadhaarlogin").post(validateAadhaarLogin, async (req, res, next) => {
    try {
        return await aadhaarLogin(req, res);
    }
    catch (e) {
        next(e)
    }
});




/**
 * @swagger
 * /api/abha/abhacard:
 *   get:
 *     summary: Retrieve User ABHA Card
 *     description: Fetches the user's ABHA card details.
 *     tags:
 *       - ABHA
 *     parameters:
 *       - in: header
 *         name: X-token
 *         schema:
 *           type: string
 *         required: true
 *         description: The token required for authorization.
 *     responses:
 *       200:
 *         description: ABHA card details retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 cardDetails:
 *                   type: object
 *                   description: The details of the ABHA card.
 *       400:
 *         description: Bad request. Unable to retrieve ABHA card details.
 */
abhaRouter.route("/abhacard").get(validateUserProfile, async (req, res, next) => {
    try {
        return await getUserAbhaCard(req, res);
    }
    catch (e) {
        next(e)
    }
});
abhaRouter.route("/abhaaddresscard").get(validateUserProfile, async (req, res, next) => {
    try {
        return await getUserAbhaAddressCard(req, res);
    }
    catch (e) {
        next(e)
    }
});




// QR Code

/**
 * @swagger
 * /api/abha/qrcode:
 *   get:
 *     summary: Retrieve User QR Code
 *     description: Fetches the QR code associated with the user's account.
 *     tags:
 *       - ABHA
 *     parameters:
 *       - in: header
 *         name: X-token
 *         schema:
 *           type: string
 *         required: true
 *         description: The token required for authorization.
 *     responses:
 *       200:
 *         description: QR code retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 qrCode:
 *                   type: string
 *                   description: The QR code for the user's account.
 *       400:
 *         description: Bad request. Unable to retrieve QR code.
 */

abhaRouter.route("/qrcode").get(async (req, res, next) => {
    try {
        return await getQRCode(req, res);
    }
    catch (e) {
        next(e)
    }
});




// Create Abha

/**
 * @swagger
 * /api/abha/createabhacard:
 *   post:
 *     summary: Create ABHA Card
 *     description: Initiates the process to create an ABHA card using the Aadhaar number.
 *     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               aadhaarNumber:
 *                 type: string
 *                 description: The Aadhaar number of the user to create an ABHA card.
 *     responses:
 *       200:
 *         description: OTP request initiated successfully for ABHA card creation.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 txnId:
 *                   type: string
 *                   description: The transaction ID for the OTP request.
 *       400:
 *         description: Bad request. Unable to create ABHA card.
 */
abhaRouter.route("/createabhacard").post(validateAadhaarLogin, async (req, res, next) => {
    try {
        return await createAbhaCard(req, res);
    }
    catch (e) {
        next(e)
    }
});



/**
 * @swagger
 * /api/abha/enrollbyaadhaar:
 *   post:
 *     summary: Enroll ABHA Card by Aadhaar
 *     description: Completes the ABHA card enrollment process using the Aadhaar OTP and mobile number.
 *     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               mobileNumber:
 *                 type: string
 *                 description: The mobile number linked to the Aadhaar account for receiving OTP.
 *               otp:
 *                 type: string
 *                 description: The OTP received on the mobile number for verification.
 *               txnId:
 *                 type: string
 *                 description: The transaction ID for the OTP request.
 *     responses:
 *       200:
 *         description: Enrollment completed successfully with ABHA card created.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 abhaNumber:
 *                   type: string
 *                   description: The generated ABHA number.
 *       400:
 *         description: Bad request. Unable to complete enrollment.
 */
abhaRouter.route("/enrollbyaadhaar").post(validateEnrollByAadhaar, async (req, res, next) => {
    try {
        return await enrollByAadhaar(req, res);
    }
    catch (e) {
        next(e)
    }
});


abhaRouter.route("/enrollbymobile").post(validateEnrollByMobile, async (req, res, next) => {
    try {
        return await enrollbyMobile(req, res);
    }
    catch (e) {
        next(e)
    }
});

abhaRouter.route("/enrollmobileotp").post(validateEnrollByMobileOtp, async (req, res, next) => {
    try {
        return await enrollMobileOTP(req, res);
    }
    catch (e) {
        next(e)
    }
});



/**
 * @swagger
 * /api/abha/suggestion:
 *   get:
 *     summary: Get Enrollment Suggestions
 *     description: Retrieves enrollment suggestions based on the transaction ID.
 *     tags:
 *       - ABHA
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               txnId:
 *                 type: string
 *                 description: The transaction ID to fetch suggestions for.
 *     responses:
 *       200:
 *         description: Suggestions retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 suggestions:
 *                   type: array
 *                   items:
 *                     type: string
 *                     description: List of suggestions based on the enrollment criteria.
 *       400:
 *         description: Bad request. Unable to fetch suggestions.
 */
abhaRouter.route("/suggestion").post(validateSuggestion, async (req, res, next) => {
    try {
        return await getSuggestion(req, res);
    }
    catch (e) {
        next(e)
    }
});


abhaRouter.route("/enrollbyabhaaddress").post(validateEnrollByAbhaAddress, async (req, res, next) => {
    try {
        return await enrollByAbhaddress(req, res);
    }
    catch (e) {
        next(e)
    }
});


abhaRouter.route("/abhanumberaddressotp").post(validateAbhaNumberAddressOtp,
    async (req, res, next) => {
        try {
            return await handleAbhaNumberAddressOtpRequest(req, res)

        } catch (e) {
            next(e)
        }
    }
)
abhaRouter.route("/verifyabhanumberaddressotp").post(validateVerifyAbhaNumberAddressOtp,
    async (req, res, next) => {
        try {
            return await handleVerifyAbhaNumberAddressOtp(req, res)

        } catch (e) {
            next(e)
        }
    }
)

abhaRouter.route("/profileonshare").post(async (req, res, next) => {
    try {
        return await profileOnShare(req, res);
    }
    catch (e) {
        next(e)
    }
});
abhaRouter.route(FHIR_FROM_CLINIC).post( async (req, res, next) => {
    try {
        return await fhirRequestController(req, res);
    } catch (error) {
        next(error);
    }
});


export default abhaRouter;
