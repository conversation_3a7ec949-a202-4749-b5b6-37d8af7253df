
export class AbhaResponse {
    constructor(success, result) {
      this.isSuccess = success;
      this.response = result;
    }
  }
  
export const SEARCH_LOGIN_URL="/profile/account/abha/search";
export const SEARCH_ABHAADDRESS="/phr/web/login/abha/search"
export const ABHA_REQUEST_OTP_URL="/phr/web/login/abha/request/otp"
export const ABHA_VERIFY_OTP_URL="/phr/web/login/abha/verify"
export const ABHA_REQUEST_AADHAR_URL="/phr/web/login/abha/request/otp"
export const ABHA_VERIFY_AADHAR_OTP_URL="/phr/web/login/abha/verify"
export const MOBILE_LOGIN_URL="/profile/login/request/otp"
// export const INDEX_LOGIN_REQUEST_URL="/profile/login/request/otp"
export const VERIFY_USER_URL="/profile/login/verify/user"
export const GET_USER_PROFILE_URL="/profile/account"
export const GET_USER_ABHA_PROFILE_URL="/phr/web/login/profile/abha-profile"
export const PROFILE_LOGIN_REQUEST_URL="/profile/login/request/otp"
export const ABHA_LOGIN_URL="/profile/login/request/otp"
export const VERIFY_ABHA_AADHAR_OTP_URL="/profile/login/verify"
export const PROFILE_LOGIN_VERIFY_URL="/profile/login/verify"

export const GET_ABHA_CARD_API_URL="/profile/account/abha-card"
export const GET_ABHA_ADDRESSCARD_API_URL="/phr/web/login/profile/abha/phr-card"
export const QR_CODE_API_URL="/profile/account/qrCode"
export const ABHA_ENROLL_URL="/enrollment/request/otp"

export const ENROLL_BY_AADHAR_API_URL="/enrollment/enrol/byAadhaar"
export const ENROLL_REQUEST_OTP_API_URL="/enrollment/request/otp"
export const ENROLL_MOBILE_NO_OTP_API_URL="/enrollment/auth/byAbdm"
export const GET_SUGGESTION_API_URL="/enrollment/enrol/suggestion"
export const ENROLL_BY_ABHA_ADDRESS_API_URL="/enrollment/enrol/abha-address"

export const PROFILE_ON_SHARE = '/api/hiecm/patient-share/v3/on-share'
export const PROFILE_SHARE_WEBHOOK = '/api/v3/hip/patient/share'

export const CONSENT_REQUEST_INIT = "/consent/v3/request/init";
export const CONSENT_ON_INIT = '/api/v3/hiu/consent/request/on-init'

export const HIU_CONSENT_NOTIFY = '/api/v3/hiu/consent/request/notify'

export const HIP_CONSENT_NOTIFY = '/api/v3/consent/request/hip/notify'

export const CONSENT_REQUEST_STATUS = '/consent/v3/request/status'
export const CONSENT_ON_STATUS = "/api/v3/hiu/consent/request/on-status"
export const CONSENT_REQUEST_ON_NOTIFY = "/consent/v3/request/hiu/on-notify";
export const FETCH_CONSENT = "/consent/v3/fetch";
export const CONSENT_REQUEST_ON_FETCH = "/api/v3/hiu/consent/on-fetch";

export const DATA_FLOW_HEALTHINFORMATION_REQUEST="/data-flow/v3/health-information/request";
export const DATA_FLOW_HEALTHINFORMATION_ON_REQUEST="/api/v3/hiu/health-information/on-request";
export const ON_NOTIFY_CONSENT_REQUEST="/api/hiecm/consent/v3/request/hiu/on-notify"
export const SESSION_API_URL ="/api/hiecm/gateway/v3/sessions"
export const DATA_PUSH_URL ="/data/push"



// m2 apis
export const LINKING_TOKEN="/hiecm/api/v3/token/generate-token"
export const ON_GENERATE_TOKEN="/api/v3/hip/token/on-generate-token"
export const ON_GENERATE_TOKEN2="/api/v3/token/on-generate-token"
export const LINK_CARE_CONTEXT="/api/hiecm/hip/v3/link/carecontext"
export const ON_LINK_CARECONTEXT="/api/v3/link/on_carecontext"
export const ON_DISCOVER="/api/hiecm/user-initiated-linking/v3/patient/care-context/on-discover"
export const DISCOVER_HEALTH_RECORDS="/api/v3/hip/patient/care-context/discover"

export const INIT_CARECONTEXT="/api/v3/hip/link/care-context/init"

export const CARECONTEXT_ON_INIT="/api/hiecm/user-initiated-linking/v3/link/care-context/on-init"
export const CONFIRM_INIT="/api/v3/hip/link/care-context/confirm"
export const FHIR_FROM_CLINIC = '/fhir'
export const ON_CONFIRM="/api/hiecm/user-initiated-linking/v3/link/care-context/on-confirm"

// data flow 
export const HIP_ON_NOTIFY_DATAPUSH="/api/hiecm/consent/v3/request/hip/on-notify"

export const REQUEST_M2="/api/v3/hip/health-information/request"
export const ON_REQUEST_M2="/api/v3/hiu/health-information/on-request"
export const ON_REQUEST="/api/hiecm/data-flow/v3/health-information/hip/on-request"
// export const NOTIFY_HEALTHINFORMATION="/api/hiecm/data-flow/v3/health-information/notify"
export const NOTIFY_HEALTHINFORMATION="/hiecm/api/v3/data-flow/health-information/notify"

// export const NOTIFY_HEALTHINFORMATION_HIU="/api/hiecm/data-flow/v3/health-information/notify"
export const NOTIFY_HEALTHINFORMATION_HIU="/hiecm/api/v3/data-flow/health-information/notify"
