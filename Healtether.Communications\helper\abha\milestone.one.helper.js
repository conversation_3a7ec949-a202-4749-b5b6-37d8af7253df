import dotenv from "dotenv";
import { v4 as uuidv4 } from "uuid";
import {
  <PERSON>HA_ENROLL_URL,
  ABHA_REQUEST_AADHAR_URL,
  ABHA_VERIFY_AADHAR_OTP_URL,
  ABHA_VERIFY_OTP_URL,
  AbhaResponse,
  ENROLL_BY_AADHAR_API_URL,
  ENROLL_BY_ABHA_ADDRESS_API_URL,
  ENROLL_MOBILE_NO_OTP_API_URL,
  ENROLL_REQUEST_OTP_API_URL,
  GET_ABHA_ADDRESSCARD_API_URL,
  GET_ABHA_CARD_API_URL,
  GET_SUGGESTION_API_URL,
  GET_USER_ABHA_PROFILE_URL,
  GET_USER_PROFILE_URL,
  PROFILE_LOGIN_REQUEST_URL,
  PROFILE_LOGIN_VERIFY_URL,
  QR_CODE_API_URL,
  SEARCH_ABHAADDRESS,
  SEARCH_LOGIN_URL,
  VERIFY_<PERSON>HA_AADHAR_OTP_URL,
  VERIFY_USER_URL,
} from "../../utils/abha.api.js";
import encryptData from "./encrypt_function.js";
import { AbdmAccessToken } from "./abdm-access-token.js";
import { fetchAbhaApi } from "./abdm.helper.js";

dotenv.config();

const base_url = process.env.ABHA_BASE_URL;
const base_url_2 = process.env.ABHA_M2_BASE_URL;

export const searchLogin = async (mobile) => {
  const encryptValue = encryptData(mobile.toString());
  var result = await fetchAbhaApi(SEARCH_LOGIN_URL, {
    scope: ["search-abha"],
    mobile: encryptValue,
  });
  const response = await result.json();
  return new AbhaResponse(result.ok, response);
};


export const handleAddressSearch = async (address) => {
  var result = await fetchAbhaApi(SEARCH_ABHAADDRESS, {
    abhaAddress:address ,
  });
  const response = await result.json();
  return new AbhaResponse(result.ok, response);
};

export const loginUsingIndexMobile = async (index, txnId) => {
  const encryptValue = encryptData(index.toString());
  var result = await fetchAbhaApi(PROFILE_LOGIN_REQUEST_URL, {
    scope: ["abha-login", "search-abha", "mobile-verify"],
    loginHint: "index",
    loginId: encryptValue,
    otpSystem: "abdm",
    txnId: txnId,
  });
  const response = await result.json();
  return new AbhaResponse(result.ok, response);
};

export const abhaNumberAddressotp = async ({
  type,
  abhaNumberAddress,
  otpSystem = "aadhaar"
}) => {
  let url, scope, loginHint;
  switch (type) {
    case "abhaaddressAadharOtp":
      url = ABHA_REQUEST_AADHAR_URL;
      scope = ["abha-address-login", "aadhaar-verify"];
      loginHint = "abha-address";
      break;
    case "abhaAddressMobileOtp":
      url = ABHA_REQUEST_AADHAR_URL;
      scope = ["abha-address-login", "mobile-verify"];
      loginHint = "abha-address";
      otpSystem = "abdm";
      break;
    case "abhaNumberAadhaarLogin":
      url = PROFILE_LOGIN_REQUEST_URL;
      scope = ["abha-login", "aadhaar-verify"];
      loginHint = "abha-number";
      break;
    case "abhaNumberMobileLogin":
      url = PROFILE_LOGIN_REQUEST_URL;
      scope = ["abha-login", "mobile-verify"];
      loginHint = "abha-number";
      otpSystem = "abdm";
      break;
    default:
      throw new Error("Invalid request type");
  }
  const encryptedIdentifier = encryptData(abhaNumberAddress.toString());
  const result = await fetchAbhaApi(url, {
    scope,
    loginHint,
    loginId: encryptedIdentifier,
    otpSystem,
  });

  const response = await result.json();
  return new AbhaResponse(result.ok, response);
};



// verify otp one api 
export const verifyAbhanumberAddressOtp = async (type, otp, txnId) => {
  let url, scope;
  console.log("type", type);
  switch (type) {
    case "verifyAbhaAadhaarOtp":
      url = ABHA_VERIFY_AADHAR_OTP_URL;
      scope = ["abha-address-login", "aadhaar-verify"];
      break;
    case "verifyAbhaMobileOtp":
      url = ABHA_VERIFY_OTP_URL;
      scope = ["abha-address-login", "mobile-verify"];
      break;
    case "verifyAbhaNumberAadhaarOtp":
      url = VERIFY_ABHA_AADHAR_OTP_URL;
      scope = ["abha-login", "aadhaar-verify"];
      break;
    case "verifyAbhaNumberMobileOtp":
      url = PROFILE_LOGIN_VERIFY_URL;
      scope = ["abha-login", "mobile-verify"];
      break;
    default:
      throw new Error("Invalid verification type");
  }

  const encryptedOtp = encryptData(otp.toString());
  const result = await fetchAbhaApi(url, {
    scope,
    authData: {
      authMethods: ["otp"],
      otp: {
        otpValue: encryptedOtp,
        txnId,
      },
    },
  });

  const response = await result.json();
  return new AbhaResponse(result.ok, response);
};



export const verifyMobileNoOtp = async (otp, txnId) => {
  console.log(otp, txnId)
  const encryptValue = encryptData(otp.toString());
  var result = await fetchAbhaApi(PROFILE_LOGIN_VERIFY_URL, {
    scope: ["abha-login", "mobile-verify"],
    authData: {
      authMethods: ["otp"],
      otp: {
        txnId: txnId,
        otpValue: encryptValue,
      },
    },
  });
  const response = await result.json();
  return new AbhaResponse(result.ok, response);
};


export const userProfile = async (xToken) => {
  var result = await fetchAbhaApi(
    GET_USER_PROFILE_URL,
    "",
    "GET",
    "",
    "",
    xToken
  );
  const response = await result.json();
  return new AbhaResponse(result.ok, response);
};

export const userAbhaAddressProfile = async (xToken) => {
  var result = await fetchAbhaApi(
    GET_USER_ABHA_PROFILE_URL,
    "",
    "GET",
    "",
    "",
    xToken
  );
  const response = await result.json();
  return new AbhaResponse(result.ok, response);
};



export const loginUsingAadhar = async (aadhaarNumber) => {
  const encryptValue = encryptData(aadhaarNumber.toString());
  var result = await fetchAbhaApi(PROFILE_LOGIN_REQUEST_URL, {
    loginHint: "aadhaar",
    loginId: encryptValue,
    otpSystem: "aadhaar",
    scope: ["abha-login", "aadhaar-verify"],
  });
  const response = await result.json();
  return new AbhaResponse(result.ok, response);
};


export const getAbhaCard = async (xToken) => {
  console.log("xtoken", xToken);
  const result = await fetchAbhaApi(
    GET_ABHA_CARD_API_URL,
    "",
    "GET",
    "",
    "",
    xToken
  );

  // Convert the response to an ArrayBuffer and then to a Node.js Buffer
  const arrayBuffer = await result.arrayBuffer();
  const imageBuffer = Buffer.from(arrayBuffer);
  return {
    isSuccess: result.ok,
    response: imageBuffer, // Return the image buffer
  };
};
export const getAbhaAddressCard = async (xToken) => {
  console.log("xtoken", xToken);
  const result = await fetchAbhaApi(
    GET_ABHA_ADDRESSCARD_API_URL,
    "",
    "GET",
    "",
    "",
    xToken
  );

  // Convert the response to an ArrayBuffer and then to a Node.js Buffer
  const arrayBuffer = await result.arrayBuffer();
  const imageBuffer = Buffer.from(arrayBuffer);
  return {
    isSuccess: result.ok,
    response: imageBuffer, // Return the image buffer
  };
};


export const qrCode = async (xToken) => {
  var result = await fetchAbhaApi(QR_CODE_API_URL, {}, "GET", "", "", xToken);

  // Convert the response to an ArrayBuffer and then to a Node.js Buffer
  const arrayBuffer = await result.arrayBuffer();
  const imageBuffer = Buffer.from(arrayBuffer);
  return new AbhaResponse(result.ok, imageBuffer);
};

export async function abhaCardCreation(aadhaarNumber) {
  const encryptValue = encryptData(aadhaarNumber.toString());
  var result = await fetchAbhaApi(ABHA_ENROLL_URL, {
    scope: ["abha-enrol"],
    loginHint: "aadhaar",
    loginId: encryptValue,
    otpSystem: "aadhaar",
  });
  const response = await result.json();
  return new AbhaResponse(result.ok, response);
};

export const enrollUsingAadhaar = async (mobileNumber, otp, txnId) => {
  const isoTimestamp = new Date().toISOString();
  const encryptValue = encryptData(otp.toString());
  var result = await fetchAbhaApi(ENROLL_BY_AADHAR_API_URL, {
    authData: {
      authMethods: ["otp"],
      otp: {
        timeStamp: isoTimestamp,
        txnId: txnId,
        otpValue: encryptValue,
        mobile: mobileNumber,
      },
    },
    consent: {
      code: "abha-enrollment",
      version: "1.4",
    },
  });

  const response = await result.json();
  return new AbhaResponse(result.ok, response);
};

export const enrollUsingMobile = async (mobile, txnId) => {
  const encryptValue = encryptData(mobile.toString());
  var result = await fetchAbhaApi(ENROLL_REQUEST_OTP_API_URL, {
    txnId: txnId,
    scope: ["abha-enrol", "mobile-verify"],
    loginHint: "mobile",
    loginId: encryptValue,
    otpSystem: "abdm",
  });

  const response = await result.json();
  return new AbhaResponse(result.ok, response);
};

export const enrollMobileNoOTP = async (mobileNumber, otp, txnId) => {
  const isoTimestamp = new Date().toISOString();
  const encryptValue = encryptData(otp.toString());
  var result = await fetchAbhaApi(ENROLL_MOBILE_NO_OTP_API_URL, {
    scope: ["abha-enrol", "mobile-verify"],
    authData: {
      authMethods: ["otp"],
      otp: {
        timeStamp: isoTimestamp,
        txnId: txnId,
        otpValue: encryptValue,
      },
    },
  });

  const response = await result.json();
  return new AbhaResponse(result.ok, response);
};

export const getAbhaAddressSuggestion = async (txnId) => {
  var result = await fetchAbhaApi(GET_SUGGESTION_API_URL, "", "GET", txnId, "", "");

  const response = await result.json();
  console.log("response:", response);

  return new AbhaResponse(result.ok, response);
};

export const enrollUsingAbhaddress = async (txnId, abhaAddress, preferred) => {
  var result = await fetchAbhaApi(ENROLL_BY_ABHA_ADDRESS_API_URL, {
    txnId: txnId,
    abhaAddress: abhaAddress,
    preferred: preferred,
  });

  const response = await result.json();
  return new AbhaResponse(result.ok, response);
};


export const patientProfileOnShare = async (acknowledgement, response, req) => {
  console.log("mockreqest data", req.webhookData.data);
  const pre = await fetchWebhookResponse(req);
  const requestId = pre.headers["request-id"][0];
  // const pre = req.webhookData;
  // const requestId = pre.headers["request-id"][0];

  // console.log("requestId",requestId)

  const requestBody = {
    acknowledgement: {
      abhaAddress: pre?.data?.profile?.patient?.abhaAddress,
      status: "SUCCESS",
      profile: {
        context: pre?.data?.metaData?.context,
        tokenNumber: "15",
        expiry: "3600",
      },
    },
    response: {
      requestId: requestId,
    },
  };
  console.log("requestBody", requestBody);
  const result = await fetchAbhaApi(
    PROFILE_ON_SHARE, // Endpoint
    requestBody, // Body
    "POST", // Method
    {
      "X-CM-ID": cmId,
    }
  );
  const profileRes = await result.json();
  console.log("successResponse", profileRes);

  // ()=>stopWebhookPolling()
  const nameParts = pre?.data?.profile?.patient?.name
    .split(" ")
    .filter(Boolean);
  console.error("error", nameParts, result.statusCode);
  // if(result.statusCode==200){
  console.log(process.env.CLINIC_SERVER_URL);
  let createAppointment = await fetch(
    `${process.env.CLINIC_SERVER_URL}/appointment/bookappointmentforscannshare`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        data: {
          name: `${nameParts[0]} ${nameParts[1]}`,
          firstName: `${nameParts[0]}`,
          lastName: `${nameParts[1]}`,
          gender:
            pre?.data?.profile?.patient?.gender == "F" ? "Female" : "Male",
          birthDate: new Date(
            Date.UTC(
              parseInt(pre?.data?.profile?.patient?.yearOfBirth),
              parseInt(pre?.data?.profile?.patient?.monthOfBirth) - 1,
              parseInt(pre?.data?.profile?.patient?.dayOfBirth)
            )
          ),
          appointmentDate: new Date(Date.now()),
          clientId: `${pre?.data?.metaData?.context}`,
          abhaNumber: `${pre?.data?.profile?.patient?.abhaNumber}`,
          abhaAddress: `${pre?.data?.profile?.patient?.abhaAddress}`,
          mobile: `${pre?.data?.profile?.patient?.phoneNumber}`,
          OpType: "Scan & Share",
        },
      }),
    }
  );

  let appointmentRes = await createAppointment.json();
  console.log(appointmentRes, "Appointment created successfully");
  return new AbhaResponse(result.ok, appointmentRes);
};

